import os.path
import numpy as np
import pandas as pd
import torch
import esm
import argparse
import os

# Global variables to store model and alphabet (avoid reloading)
model = None
alphabet = None
batch_converter = None
device = None

def initialize_model(use_gpu=True, gpu_id=0):
    """Initialize the ESM model and move to GPU if available"""
    global model, alphabet, batch_converter, device

    # Check if CUDA is available and user wants to use GPU
    if use_gpu and torch.cuda.is_available():
        try:
            device = torch.device(f'cuda:{gpu_id}')
            print(f"CUDA is available. Attempting to use GPU: {device}")
            print(f"GPU Name: {torch.cuda.get_device_name(gpu_id)}")
            print(f"GPU Memory: {torch.cuda.get_device_properties(gpu_id).total_memory / 1024**3:.1f} GB")

            # Test GPU functionality with a simple operation
            test_tensor = torch.randn(10, 10).to(device)
            _ = test_tensor @ test_tensor.T
            print("GPU test successful!")

        except Exception as e:
            print(f"GPU initialization failed: {e}")
            print("Falling back to CPU")
            device = torch.device('cpu')
            use_gpu = False
    else:
        device = torch.device('cpu')
        if use_gpu:
            print("CUDA not available, falling back to CPU")
        else:
            print("Using CPU as requested")

    # Load model and alphabet
    print("Loading ESM model...")
    model, alphabet = esm.pretrained.esm2_t12_35M_UR50D()
    batch_converter = alphabet.get_batch_converter()

    # Move model to device with error handling
    try:
        model = model.to(device)
        model.eval()
        print(f"Model loaded and moved to {device}")

        # Clear cache if using GPU
        if device.type == 'cuda':
            torch.cuda.empty_cache()

    except Exception as e:
        print(f"Failed to move model to {device}: {e}")
        print("Falling back to CPU")
        device = torch.device('cpu')
        model = model.to(device)
        model.eval()
        print(f"Model loaded and moved to {device}")

def get_pretrained_embedding(s, use_gpu=True, gpu_id=0):
    """Generate protein embedding using ESM model on GPU"""
    global model, alphabet, batch_converter, device

    # Initialize model if not already done
    if model is None:
        initialize_model(use_gpu, gpu_id)

    _, _, batch_tokens = batch_converter([("protein", s)])
    batch_lens = (batch_tokens != alphabet.padding_idx).sum(1)

    # Move tokens to device with error handling
    try:
        batch_tokens = batch_tokens.to(device)
        batch_lens = batch_lens.to(device)

        # Extract per-residue representations
        with torch.no_grad():
            results = model(batch_tokens, repr_layers=[12], return_contacts=False)  # Disable contacts to save memory
        token_representations = results["representations"][12]

        # Generate per-sequence representations via averaging
        # NOTE: token 0 is always a beginning-of-sequence token, so the first residue is token 1.
        sequence_representations = []
        for i, tokens_len in enumerate(batch_lens):
            # Move back to CPU for numpy conversion
            seq_repr = token_representations[i, 1: tokens_len - 1].cpu()
            sequence_representations.append(seq_repr)

        return sequence_representations[0]

    except Exception as e:
        print(f"Error during inference on {device}: {e}")
        print("Attempting to fall back to CPU...")

        # Fallback to CPU
        device_cpu = torch.device('cpu')
        model = model.to(device_cpu)

        batch_tokens = batch_tokens.to(device_cpu)
        batch_lens = batch_lens.to(device_cpu)

        with torch.no_grad():
            results = model(batch_tokens, repr_layers=[12], return_contacts=False)
        token_representations = results["representations"][12]

        sequence_representations = []
        for i, tokens_len in enumerate(batch_lens):
            seq_repr = token_representations[i, 1: tokens_len - 1]
            sequence_representations.append(seq_repr)

        return sequence_representations[0]

def generate_feature(args):

    data_path = args.root_data_path
    dataset = args.dataset
    output_data_path = data_path + '/' + dataset + '/protein/'

    if not os.path.exists(output_data_path):
        # 如果文件夹不存在，则创建
        os.makedirs(output_data_path)
        print(f"{output_data_path} created")
    else:
        print(f"{output_data_path} exists")



    opt = ['train', 'test']

    id_list = []
    count = 0


    for t in opt:
        if dataset == 'davis':
            raw_data = pd.read_csv(f'{data_path}/{dataset}.csv')
        else:
            raw_data = pd.read_csv(f'{data_path}/{dataset}_{t}.csv')
        sequence_values = raw_data['target_sequence'].values
        for i, s in enumerate(sequence_values):
            id = raw_data['target_id'][i]
            if id in id_list:
                continue
            if os.path.isfile(f'{output_data_path}' + id + '.npy'):
                continue
            seq_emb = get_pretrained_embedding(s, args.use_gpu, args.gpu_id)
            print(seq_emb.shape)
            np.save(f'{output_data_path}' + id, seq_emb)
            print(raw_data['target_id'][i])
            id_list.append(id)
            count += 1

            # Clear GPU cache periodically to prevent memory issues
            if args.use_gpu and torch.cuda.is_available() and count % 10 == 0:
                torch.cuda.empty_cache()

    print(f"Total proteins processed: {count}")

    # Final cleanup
    if args.use_gpu and torch.cuda.is_available():
        torch.cuda.empty_cache()
        print("GPU memory cleared")


def parse_args():
    parser = argparse.ArgumentParser()

    parser.add_argument('--root_data_path', type=str, default='./data', help='Raw Data Path')
    parser.add_argument('--dataset', type=str, default='davis', help='Datasets')
    parser.add_argument('--use_gpu', action='store_true', help='Use GPU if available')
    parser.add_argument('--gpu_id', type=int, default=0, help='GPU ID to use')
    return parser.parse_args()

if __name__ == '__main__':
    params = parse_args()
    print(params)

    # Print GPU information if available
    if torch.cuda.is_available():
        print(f"CUDA Version: {torch.version.cuda}")
        print(f"Available GPUs: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"  GPU {i}: {torch.cuda.get_device_name(i)}")
    else:
        print("CUDA not available")

    generate_feature(params)


