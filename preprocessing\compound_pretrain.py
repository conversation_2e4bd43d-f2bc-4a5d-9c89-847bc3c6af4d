import pickle
# from deepchem.feat import Smiles<PERSON>okenizer
from transformers import <PERSON><PERSON><PERSON><PERSON>, <PERSON>Token<PERSON>,Bert<PERSON>odel,<PERSON>Model,RobertaTokenizer
import argparse
import os
import os.path
import numpy as np
import pandas as pd
import torch

# Global variables to store model and tokenizer (avoid reloading)
tokenizer = None
model = None
device = None

def initialize_model(use_gpu=True, gpu_id=0):
    """Initialize the ChemBERTa model and move to GPU if available"""
    global tokenizer, model, device

    # Check if CUDA is available and user wants to use GPU
    if use_gpu and torch.cuda.is_available():
        try:
            device = torch.device(f'cuda:{gpu_id}')
            print(f"CUDA is available. Attempting to use GPU: {device}")
            print(f"GPU Name: {torch.cuda.get_device_name(gpu_id)}")
            print(f"GPU Memory: {torch.cuda.get_device_properties(gpu_id).total_memory / 1024**3:.1f} GB")

            # Test GPU functionality with a simple operation
            test_tensor = torch.randn(10, 10).to(device)
            _ = test_tensor @ test_tensor.T
            print("GPU test successful!")

        except Exception as e:
            print(f"GPU initialization failed: {e}")
            print("Falling back to CPU")
            device = torch.device('cpu')
            use_gpu = False
    else:
        device = torch.device('cpu')
        if use_gpu:
            print("CUDA not available, falling back to CPU")
        else:
            print("Using CPU as requested")

    # Load tokenizer and model
    print("Loading ChemBERTa model...")
    tokenizer = AutoTokenizer.from_pretrained("DeepChem/ChemBERTa-77M-MLM")
    model = RobertaModel.from_pretrained("DeepChem/ChemBERTa-77M-MLM")

    # Move model to device with error handling
    try:
        model = model.to(device)
        model.eval()
        print(f"Model loaded and moved to {device}")

        # Clear cache if using GPU
        if device.type == 'cuda':
            torch.cuda.empty_cache()

    except Exception as e:
        print(f"Failed to move model to {device}: {e}")
        print("Falling back to CPU")
        device = torch.device('cpu')
        model = model.to(device)
        model.eval()
        print(f"Model loaded and moved to {device}")

def get_smiles_embedding_batch(smiles_list, use_gpu=True, gpu_id=0, batch_size=32):
    """Generate SMILES embeddings in batches for better GPU utilization"""
    global tokenizer, model, device

    # Initialize model if not already done
    if model is None:
        initialize_model(use_gpu, gpu_id)

    embeddings_dict = {}

    try:
        # Process in batches
        for i in range(0, len(smiles_list), batch_size):
            batch_smiles = smiles_list[i:i+batch_size]

            # Tokenize batch with padding
            inputs = tokenizer(batch_smiles, return_tensors='pt', padding=True, truncation=True, max_length=512)

            # Move inputs to device
            inputs = {k: v.to(device) for k, v in inputs.items()}

            # Generate embeddings
            with torch.no_grad():
                outputs = model(**inputs)

            # Extract embeddings for each SMILES in batch
            for j, smiles in enumerate(batch_smiles):
                # Get attention mask to find actual sequence length
                attention_mask = inputs['attention_mask'][j]
                seq_len = attention_mask.sum().item()

                # Extract embeddings (excluding [CLS] and [SEP] tokens)
                embeddings = outputs.last_hidden_state[j][1:seq_len-1]

                # Move back to CPU for storage
                embeddings_dict[smiles] = embeddings.cpu()

            if (i // batch_size + 1) % 10 == 0:
                print(f"Processed {min(i + batch_size, len(smiles_list))}/{len(smiles_list)} compounds...")

        return embeddings_dict

    except Exception as e:
        print(f"Error during batch inference on {device}: {e}")
        print("Falling back to single processing on CPU...")

        # Fallback to single processing on CPU
        return get_smiles_embedding_single_fallback(smiles_list)

def get_smiles_embedding_single_fallback(smiles_list):
    """Fallback function for single SMILES processing on CPU"""
    global tokenizer, model

    device_cpu = torch.device('cpu')
    model = model.to(device_cpu)

    embeddings_dict = {}

    for smiles in smiles_list:
        inputs = tokenizer(smiles, return_tensors='pt')
        inputs = {k: v.to(device_cpu) for k, v in inputs.items()}

        with torch.no_grad():
            outputs = model(**inputs)

        embeddings = outputs.last_hidden_state[0][1:outputs.last_hidden_state.shape[1]-1]
        embeddings_dict[smiles] = embeddings

    return embeddings_dict

def get_smiles_embedding(smiles, use_gpu=True, gpu_id=0):
    """Single SMILES embedding function (for backward compatibility)"""
    result = get_smiles_embedding_batch([smiles], use_gpu, gpu_id, batch_size=1)
    return result[smiles]

def generate_feature(args):
    data_path = args.root_data_path
    dataset = args.dataset
    output_data_path = data_path + '/' + dataset + '/compound/'

    if not os.path.exists(output_data_path):
        # 如果文件夹不存在，则创建
        os.makedirs(output_data_path)
        print(f"{output_data_path} created")
    else:
        print(f"{output_data_path} exists")

    dict_list = {}
    count = 0
    processed_smiles = set()

    opts = ['train', 'test']
    for o in opts:
        if dataset == 'davis':
            raw_data = pd.read_csv(f'{data_path}/{dataset}.csv')
        else:
            raw_data = pd.read_csv(f'{data_path}/{dataset}_{o}.csv')

        smiles_values = raw_data['compound_iso_smiles'].values
        print(f"Processing {len(smiles_values)} compounds from {o} set...")

        for s in smiles_values:
            if s in processed_smiles:
                continue

            try:
                smiles_embedding = get_smiles_embedding(s, args.use_gpu, args.gpu_id)
                dict_list[s] = smiles_embedding
                processed_smiles.add(s)
                count += 1

                print(f"Processed {count}: {s[:50]}{'...' if len(s) > 50 else ''}")

                # Clear GPU cache periodically to prevent memory issues
                if args.use_gpu and torch.cuda.is_available() and count % 10 == 0:
                    torch.cuda.empty_cache()

            except Exception as e:
                print(f"Error processing SMILES {s}: {e}")
                continue

    # Save the dictionary
    with open(f'{output_data_path}/mol_dict.pkl', 'wb') as file:
        pickle.dump(dict_list, file)

    print(f"Total unique compounds processed: {count}")
    print(f"Dictionary saved to: {output_data_path}/mol_dict.pkl")

    # Final cleanup
    if args.use_gpu and torch.cuda.is_available():
        torch.cuda.empty_cache()
        print("GPU memory cleared")


def parse_args():
    parser = argparse.ArgumentParser()

    parser.add_argument('--root_data_path', type=str, default='./data', help='Raw Data Path')
    parser.add_argument('--dataset', type=str, default='davis', help='Datasets')
    parser.add_argument('--use_gpu', action='store_true', help='Use GPU if available')
    parser.add_argument('--gpu_id', type=int, default=0, help='GPU ID to use')
    return parser.parse_args()

if __name__ == '__main__':
    params = parse_args()
    print(params)

    # Print GPU information if available
    if torch.cuda.is_available():
        print(f"CUDA Version: {torch.version.cuda}")
        print(f"Available GPUs: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"  GPU {i}: {torch.cuda.get_device_name(i)}")
    else:
        print("CUDA not available")

    generate_feature(params)




