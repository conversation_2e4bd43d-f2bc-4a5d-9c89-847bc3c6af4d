import os.path
import numpy as np
import pandas as pd
import torch
import esm
import argparse
import os



def get_pretrained_embedding(s):
    # Truncate very long sequences to avoid memory issues
    max_seq_length = 1000  # ESM-2 can handle up to ~1000 residues efficiently
    if len(s) > max_seq_length:
        print(f"Warning: Truncating sequence from {len(s)} to {max_seq_length} residues")
        s = s[:max_seq_length]

    model, alphabet = esm.pretrained.esm2_t12_35M_UR50D()
    batch_converter = alphabet.get_batch_converter()
    model.eval()

    batch_labels, batch_strs, batch_tokens = batch_converter([("protein", s)])
    batch_lens = (batch_tokens != alphabet.padding_idx).sum(1)

    # Extract per-residue representations - without contacts to save memory
    with torch.no_grad():
        # Move to GPU if available for faster computation
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        batch_tokens = batch_tokens.to(device)
        model = model.to(device)

        results = model(batch_tokens, repr_layers=[12], return_contacts=False)
        token_representations = results["representations"][12].cpu()  # Move back to CPU

        # Clear GPU memory
        del results
        if device.type == 'cuda':
            torch.cuda.empty_cache()

    # Generate per-sequence representations via averaging
    # NOTE: token 0 is always a beginning-of-sequence token, so the first residue is token 1.
    sequence_representations = []
    for i, tokens_len in enumerate(batch_lens):
        sequence_representations.append(token_representations[i, 1: tokens_len - 1])

    return sequence_representations[0]


def generate_feature(args):

    data_path = args.root_data_path
    dataset = args.dataset
    output_data_path = data_path + '/' + dataset + '/protein/'


    opts = ['train', 'test']

    for o in opts:

        full_output_path = output_data_path + o
        if not os.path.exists(full_output_path):
            # 如果文件夹不存在，则创建
            os.makedirs(full_output_path)
            print(f"{full_output_path} created")
        else:
            print(f"{full_output_path} exists")

        count = 0
        id_list = []
        raw_data = pd.read_csv(f'{data_path}/{dataset}_{o}.csv')
        sequence_values = raw_data['target_sequence'].values
        total_sequences = len(sequence_values)

        print(f"Processing {total_sequences} sequences for {o} set...")

        for i, s in enumerate(sequence_values):
            id = str(raw_data['target_id'][i])
            if id in id_list:
                continue
            if os.path.isfile(f'{output_data_path}{o}/' + id + '.npy'):
                print(f"Skipping {id} (already exists)")
                continue

            try:
                seq_emb = get_pretrained_embedding(s.upper())
                print(f"[{i+1}/{total_sequences}] Processed {id}: {seq_emb.shape}")
                np.save(f'{output_data_path}{o}/' + id, seq_emb)
                id_list.append(id)
                count += 1
            except Exception as e:
                print(f"Error processing {id}: {str(e)}")
                continue

        print(f"Completed {o} set: {count} new embeddings generated")
    print(f"Total new embeddings: {count}")



def parse_args():
    parser = argparse.ArgumentParser()

    parser.add_argument('--root_data_path', type=str, default='./data', help='Raw Data Path')
    parser.add_argument('--dataset', type=str, default='bindingdb', help='Datasets')
    parser.add_argument('--use_gpu', action='store_true', help='Use GPU for ESM-2 model')
    parser.add_argument('--gpu_id', type=int, default=0, help='GPU ID to use')
    return parser.parse_args()

if __name__ == '__main__':
    params = parse_args()
    print(params)
    generate_feature(params)




