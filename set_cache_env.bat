@echo off
echo Setting cache directories to D drive...

REM Set pip cache directory
set PIP_CACHE_DIR=D:\Installer package\pip\cache

REM Set Hugging Face cache directory  
set HF_HOME=D:\Installer package\huggingface\cache
set TRANSFORMERS_CACHE=D:\Installer package\huggingface\cache

REM Set conda package cache (if needed)
set CONDA_PKGS_DIRS=D:\Installer package\conda\pkgs

echo Cache directories set:
echo PIP_CACHE_DIR=%PIP_CACHE_DIR%
echo HF_HOME=%HF_HOME%
echo TRANSFORMERS_CACHE=%TRANSFORMERS_CACHE%

echo.
echo To make these permanent, add them to your system environment variables.
pause
